-- 小梅花AI客服助手 - 完整集成数据库系统
-- 版本: v1.2.0 Complete Integration (MySQL 5.7.44兼容版)
-- 创建时间: 2024-12-19
-- 修复时间: 2024-12-19
-- 兼容性: 专为MySQL 5.7.44 + phpMyAdmin 5.1.1优化
-- 测试环境: MySQL 5.7.44-log + phpMyAdmin 5.1.1

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- 1. API密钥管理表
CREATE TABLE IF NOT EXISTS `api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `key_type` enum('primary','backup','secure') NOT NULL DEFAULT 'primary',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `permissions` text,
  `rate_limit` int(11) DEFAULT '1000',
  `expires_at` timestamp NULL DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_key_type` (`key_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. 卡密管理表 (兼容PHP代码)
CREATE TABLE IF NOT EXISTS `license_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_value` varchar(255) NOT NULL,
  `license_key` varchar(255) DEFAULT NULL,
  `type` enum('hourly','daily','monthly','half_yearly','yearly') NOT NULL DEFAULT 'monthly',
  `key_type` enum('trial','basic','premium','enterprise') NOT NULL DEFAULT 'basic',
  `status` enum('active','disabled','expired','deleted','unused','used','banned') NOT NULL DEFAULT 'active',
  `function_config` text,
  `script_id` int(11) DEFAULT NULL,
  `max_activations` int(11) DEFAULT '1',
  `current_activations` int(11) DEFAULT '0',
  `expires_at` timestamp NULL DEFAULT NULL,
  `expiry_date` timestamp NULL DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `last_script_check` datetime DEFAULT NULL,
  `last_heartbeat` timestamp NULL DEFAULT NULL,
  `last_used_ip` varchar(45) DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_by` varchar(100) DEFAULT NULL,
  `notes` text,
  `store_name` varchar(200) DEFAULT NULL,
  `wechat_store_id` varchar(100) DEFAULT NULL,
  `user_wechat` varchar(100) DEFAULT NULL,
  `has_customer_service` tinyint(1) NOT NULL DEFAULT '1',
  `has_product_listing` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_value` (`key_value`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_key_type` (`key_type`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_expiry_date` (`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 3. 脚本代码安全存储表
CREATE TABLE IF NOT EXISTS `secure_scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `script_name` varchar(200) NOT NULL,
  `script_version` varchar(50) NOT NULL DEFAULT '1.0.0',
  `script_type` enum('customer_service','product_listing','full_featured') NOT NULL DEFAULT 'customer_service',
  `script_content` longtext NOT NULL,
  `encryption_method` varchar(50) NOT NULL DEFAULT 'aes256',
  `content_hash` varchar(64) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `min_version` varchar(50) DEFAULT NULL,
  `max_version` varchar(50) DEFAULT NULL,
  `feature_flags` text,
  `created_by` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_script_type` (`script_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. 脚本表 (兼容PHP代码)
CREATE TABLE IF NOT EXISTS `scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `content` longtext,
  `script_code` longtext,
  `loader_code` longtext,
  `version` varchar(50) NOT NULL DEFAULT '1.0.0',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `default_ai_enabled` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. 客户端会话管理表
CREATE TABLE IF NOT EXISTS `client_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_token` varchar(255) NOT NULL,
  `license_key` varchar(255) NOT NULL,
  `client_fingerprint` varchar(64) NOT NULL,
  `api_key_id` int(11) NOT NULL DEFAULT '0',
  `client_info` text,
  `shop_info` text,
  `last_heartbeat` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_license_key` (`license_key`),
  KEY `idx_api_key_id` (`api_key_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. API请求日志表
CREATE TABLE IF NOT EXISTS `api_request_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `request_id` varchar(64) NOT NULL,
  `endpoint` varchar(200) NOT NULL,
  `method` varchar(10) NOT NULL DEFAULT 'GET',
  `api_key_id` int(11) DEFAULT NULL,
  `license_key` varchar(255) DEFAULT NULL,
  `client_ip` varchar(45) NOT NULL DEFAULT '',
  `user_agent` text,
  `request_data` text,
  `response_status` int(11) DEFAULT NULL,
  `response_time` int(11) DEFAULT NULL,
  `error_message` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_api_key_id` (`api_key_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 7. API日志表 (兼容PHP代码)
CREATE TABLE IF NOT EXISTS `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(100) NOT NULL,
  `method` varchar(10) NOT NULL,
  `license_key` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `request_data` text,
  `response_data` text,
  `status` varchar(20) DEFAULT NULL,
  `message` text,
  `status_code` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_code` (`status_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8. 服务器配置表
CREATE TABLE IF NOT EXISTS `server_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `config_type` enum('api','security','feature','system') NOT NULL DEFAULT 'system',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT '0',
  `description` text,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 9. 系统设置表
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `description` text,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 10. API密钥信息检查日志表
CREATE TABLE IF NOT EXISTS `key_info_checks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL DEFAULT '0',
  `check_type` varchar(20) DEFAULT 'API_KEY',
  `ip_address` varchar(45) NOT NULL DEFAULT '',
  `user_agent` text,
  `response_data` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_key_id` (`key_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 11. API配置版本表
CREATE TABLE IF NOT EXISTS `api_config_versions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_type` varchar(20) NOT NULL,
  `config_data` text NOT NULL,
  `version` varchar(20) NOT NULL DEFAULT '1.0.0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `activated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 12. 客户端更新通知表
CREATE TABLE IF NOT EXISTS `client_update_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL DEFAULT '0',
  `notification_type` varchar(20) NOT NULL,
  `notification_data` text,
  `status` varchar(20) DEFAULT 'PENDING',
  `priority` varchar(20) DEFAULT 'NORMAL',
  `attempts` int(11) DEFAULT '0',
  `max_attempts` int(11) DEFAULT '5',
  `last_attempt_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `received_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_key_id` (`key_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 13. 使用日志表
CREATE TABLE IF NOT EXISTS `usage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL DEFAULT '0',
  `action` varchar(50) NOT NULL,
  `details` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_key_id` (`key_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认数据
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('api_secret_key', 'xiaomeihua_default_key_change_this_immediately', 'API密钥'),
('api_version', 'v2', 'API版本'),
('primary_server', 'https://xiaomeihuakefu.cn', '主服务器地址'),
('backup_server', 'https://api.xiaomeihuakefu.cn', '备用服务器地址'),
('secure_server', 'https://secure.xiaomeihuakefu.cn', '安全服务器地址'),
('api_key_update_interval', '300', 'API密钥更新检查间隔(秒)'),
('script_update_check_enabled', '1', '是否启用脚本更新检查'),
('client_notification_enabled', '1', '是否启用客户端通知'),
('heartbeat_interval', '60', '心跳间隔(秒)'),
('database_version', '1.2.0', '数据库版本号'),
('system_status', 'active', '系统状态'),
('installation_completed', '1', '安装完成标记');

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;

SELECT '小梅花AI客服系统完整集成数据库创建完成！' as message; 