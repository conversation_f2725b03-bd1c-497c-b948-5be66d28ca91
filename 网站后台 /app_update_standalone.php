<?php
/**
 * 独立的APP更新API - 完全自包含，不依赖任何外部文件
 * 直接连接MySQL数据库，确保100%可用
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 直接连接MySQL数据库
function getDatabase() {
    try {
        $pdo = new PDO(
            "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4",
            "xiaomeihuakefu_c",
            "7Da5F1Xx995cxYz8",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 10
            ]
        );
        
        // 设置时区
        $pdo->exec("SET time_zone = '+08:00'");
        
        return $pdo;
    } catch (Exception $e) {
        sendError('数据库连接失败: ' . $e->getMessage(), 500);
    }
}

// 初始化数据库表
function initializeTables($pdo) {
    try {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'app_updates'");
        if ($stmt->rowCount() == 0) {
            // 创建表，包含所有新字段
            $createTableSQL = "
            CREATE TABLE `app_updates` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `version` varchar(50) NOT NULL COMMENT '版本号，如：1.0.1',
              `title` varchar(255) NOT NULL COMMENT '版本标题',
              `description` longtext NOT NULL COMMENT '更新说明（支持HTML）',
              `exe_download_url` varchar(500) DEFAULT NULL COMMENT 'Windows安装包直链地址',
              `exe_backup_url` varchar(500) DEFAULT NULL COMMENT 'Windows安装包备用地址',
              `dmg_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS通用版本直链地址',
              `dmg_backup_url` varchar(500) DEFAULT NULL COMMENT 'macOS通用版本备用地址',
              `dmg_m1_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS M芯片版本直链地址',
              `dmg_m1_backup_url` varchar(500) DEFAULT NULL COMMENT 'macOS M芯片版本备用地址',
              `dmg_intel_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS Intel版本直链地址',
              `dmg_intel_backup_url` varchar(500) DEFAULT NULL COMMENT 'macOS Intel版本备用地址',
              `macos_architecture_support` enum('universal','separate') DEFAULT 'universal' COMMENT 'macOS架构支持模式',
              `force_update` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否强制更新',
              `status` enum('draft','published') NOT NULL DEFAULT 'draft' COMMENT '版本状态',
              `download_count` int(11) NOT NULL DEFAULT 0 COMMENT '下载次数统计',
              `platform` enum('all','windows','macos','linux') NOT NULL DEFAULT 'all' COMMENT '支持平台',
              `min_version` varchar(50) DEFAULT NULL COMMENT '最低兼容版本',
              `release_notes` text DEFAULT NULL COMMENT '发布说明',
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_version` (`version`),
              KEY `idx_status` (`status`),
              KEY `idx_platform` (`platform`),
              KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP版本更新表'
            ";
            
            $pdo->exec($createTableSQL);
            
            // 不再插入默认的测试数据，避免出现默认的1.0.1版本
        } else {
            // 如果表已存在，检查并添加缺失的字段
            upgradeTableStructure($pdo);
        }
    } catch (Exception $e) {
        sendError('表初始化失败: ' . $e->getMessage(), 500);
    }
}

// 升级表结构，添加缺失的字段
function upgradeTableStructure($pdo) {
    try {
        // 获取现有列
        $stmt = $pdo->query("SHOW COLUMNS FROM app_updates");
        $existingColumns = [];
        while ($row = $stmt->fetch()) {
            $existingColumns[] = $row['Field'];
        }
        
        // 需要添加的新字段
        $newColumns = [
            'exe_backup_url' => "ADD COLUMN `exe_backup_url` varchar(500) DEFAULT NULL COMMENT 'Windows安装包备用地址'",
            'dmg_backup_url' => "ADD COLUMN `dmg_backup_url` varchar(500) DEFAULT NULL COMMENT 'macOS通用版本备用地址'",
            'dmg_m1_download_url' => "ADD COLUMN `dmg_m1_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS M芯片版本直链地址'",
            'dmg_m1_backup_url' => "ADD COLUMN `dmg_m1_backup_url` varchar(500) DEFAULT NULL COMMENT 'macOS M芯片版本备用地址'",
            'dmg_intel_download_url' => "ADD COLUMN `dmg_intel_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS Intel版本直链地址'",
            'dmg_intel_backup_url' => "ADD COLUMN `dmg_intel_backup_url` varchar(500) DEFAULT NULL COMMENT 'macOS Intel版本备用地址'",
            'macos_architecture_support' => "ADD COLUMN `macos_architecture_support` enum('universal','separate') DEFAULT 'universal' COMMENT 'macOS架构支持模式'"
        ];
        
        // 逐个添加缺失的字段
        foreach ($newColumns as $columnName => $alterSQL) {
            if (!in_array($columnName, $existingColumns)) {
                $pdo->exec("ALTER TABLE `app_updates` " . $alterSQL);
                error_log("Added column: " . $columnName);
            }
        }
        
        // 更新注释
        $pdo->exec("ALTER TABLE `app_updates` MODIFY COLUMN `exe_download_url` varchar(500) DEFAULT NULL COMMENT 'Windows安装包直链地址'");
        $pdo->exec("ALTER TABLE `app_updates` MODIFY COLUMN `dmg_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS通用版本直链地址'");
        
    } catch (Exception $e) {
        error_log('表升级错误: ' . $e->getMessage());
        // 不抛出错误，允许继续运行
    }
}

// 版本号验证
function validateVersion($version) {
    return preg_match('/^\d+\.\d+\.\d+$/', $version);
}

// URL验证
function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// 获取数据库连接
$pdo = getDatabase();

// 初始化表
initializeTables($pdo);

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];

// API路由
switch ($method) {
    case 'GET':
        $action = $_GET['action'] ?? 'list';
        switch ($action) {
            case 'check':
                handleCheckUpdate($pdo);
                break;
            case 'list':
                handleListVersions($pdo);
                break;
            case 'download':
                handleDownload($pdo);
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    case 'POST':
        $action = $_POST['action'] ?? '';
        switch ($action) {
            case 'create':
                handleCreateVersion($pdo);
                break;
            case 'update':
                handleUpdateVersion($pdo);
                break;
            case 'delete':
                handleDeleteVersion($pdo);
                break;
            case 'publish':
                handlePublishVersion($pdo);
                break;
            case 'unpublish':
                handleUnpublishVersion($pdo);
                break;
            case 'delete_all':
                handleDeleteAllVersions($pdo);
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    default:
        sendError('不支持的请求方法', 405);
}

/**
 * 检查更新
 */
function handleCheckUpdate($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $platform = strtolower($_GET['platform'] ?? 'all');
    
    try {
        // 获取最新发布的版本
        $stmt = $pdo->prepare("
            SELECT * FROM app_updates 
            WHERE status = 'published' AND (platform = ? OR platform = 'all')
            ORDER BY version DESC, created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$platform]);
        $latestUpdate = $stmt->fetch();
        
        if (!$latestUpdate) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'message' => '暂无可用更新'
            ], '无可用更新');
        }
        
        // 比较版本号
        $hasUpdate = version_compare($latestUpdate['version'], $currentVersion, '>');
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latestUpdate['version']
        ];
        
        if ($hasUpdate) {
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'],
                'description' => $latestUpdate['description'],
                'force_update' => (bool)$latestUpdate['force_update'],
                'download_urls' => [
                    'windows' => $latestUpdate['exe_download_url'],
                    'macos' => $latestUpdate['dmg_download_url']
                ],
                'release_notes' => $latestUpdate['release_notes']
            ];
        }
        
        sendSuccess($response, $hasUpdate ? '发现新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        sendError('检查更新失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取版本列表
 */
function handleListVersions($pdo) {
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = max(1, min(100, intval($_GET['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;

        // 获取总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM app_updates");
        $total = $stmt->fetchColumn();

        // 获取列表
        $stmt = $pdo->prepare("SELECT * FROM app_updates ORDER BY created_at DESC LIMIT ? OFFSET ?");
        $stmt->execute([$limit, $offset]);
        $versions = $stmt->fetchAll();

        sendSuccess([
            'versions' => $versions,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ], '获取版本列表成功');

    } catch (Exception $e) {
        sendError('获取版本列表失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 创建新版本
 */
function handleCreateVersion($pdo) {
    // 验证必需参数
    $version = trim($_POST['version'] ?? '');
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($title) || empty($description)) {
        sendError('标题和描述不能为空');
    }
    
    // 如果没有版本号，自动生成
    if (empty($version)) {
        $stmt = $pdo->query("SELECT version FROM app_updates ORDER BY created_at DESC LIMIT 1");
        $lastVersion = $stmt->fetchColumn();
        if ($lastVersion && preg_match('/^(\d+)\.(\d+)\.(\d+)$/', $lastVersion, $matches)) {
            $version = $matches[1] . '.' . $matches[2] . '.' . ($matches[3] + 1);
        } else {
            $version = '1.0.0';
        }
    }
    
    // 验证版本号格式
    if (!validateVersion($version)) {
        sendError('版本号格式不正确，应为：主版本号.次版本号.修订号');
    }
    
    // 获取所有下载链接
    $exeUrl = trim($_POST['exe_download_url'] ?? '');
    $exeBackupUrl = trim($_POST['exe_backup_url'] ?? '');
    $dmgUrl = trim($_POST['dmg_download_url'] ?? '');
    $dmgBackupUrl = trim($_POST['dmg_backup_url'] ?? '');
    $dmgM1Url = trim($_POST['dmg_m1_download_url'] ?? '');
    $dmgM1BackupUrl = trim($_POST['dmg_m1_backup_url'] ?? '');
    $dmgIntelUrl = trim($_POST['dmg_intel_download_url'] ?? '');
    $dmgIntelBackupUrl = trim($_POST['dmg_intel_backup_url'] ?? '');
    
    // 至少需要一个下载链接（包括备用地址）
    $hasAnyUrl = !empty($exeUrl) || !empty($exeBackupUrl) || 
                 !empty($dmgUrl) || !empty($dmgBackupUrl) ||
                 !empty($dmgM1Url) || !empty($dmgM1BackupUrl) ||
                 !empty($dmgIntelUrl) || !empty($dmgIntelBackupUrl);
    
    if (!$hasAnyUrl) {
        sendError('至少需要提供一个下载链接（Windows或macOS，包括备用地址）');
    }
    
    // 验证URL格式
    $urls = [
        'Windows直链地址' => $exeUrl,
        'Windows备用地址' => $exeBackupUrl,
        'macOS通用直链地址' => $dmgUrl,
        'macOS通用备用地址' => $dmgBackupUrl,
        'macOS M芯片直链地址' => $dmgM1Url,
        'macOS M芯片备用地址' => $dmgM1BackupUrl,
        'macOS Intel直链地址' => $dmgIntelUrl,
        'macOS Intel备用地址' => $dmgIntelBackupUrl
    ];
    
    foreach ($urls as $name => $url) {
        if (!empty($url) && !validateUrl($url)) {
            sendError($name . '格式不正确');
        }
    }
    
    try {
        // 检查版本是否已存在
        $stmt = $pdo->prepare("SELECT id FROM app_updates WHERE version = ?");
        $stmt->execute([$version]);
        if ($stmt->fetch()) {
            sendError('版本号已存在，请使用不同的版本号');
        }
        
        // 获取macOS架构相关字段
        $macosArchSupport = $_POST['macos_architecture_support'] ?? 'universal';

        // 插入新版本 - 包含所有新字段
        $stmt = $pdo->prepare("
            INSERT INTO app_updates
            (version, title, description, 
             exe_download_url, exe_backup_url,
             dmg_download_url, dmg_backup_url,
             dmg_m1_download_url, dmg_m1_backup_url,
             dmg_intel_download_url, dmg_intel_backup_url,
             macos_architecture_support,
             force_update, status, platform)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $success = $stmt->execute([
            $version,
            $title,
            $description,
            $exeUrl ?: null,
            $exeBackupUrl ?: null,
            $dmgUrl ?: null,
            $dmgBackupUrl ?: null,
            $dmgM1Url ?: null,
            $dmgM1BackupUrl ?: null,
            $dmgIntelUrl ?: null,
            $dmgIntelBackupUrl ?: null,
            $macosArchSupport,
            isset($_POST['force_update']) ? 1 : 0,
            $_POST['status'] ?? 'published',
            $_POST['platform'] ?? 'all'
        ]);
        
        if ($success) {
            $newId = $pdo->lastInsertId();
            
            // 获取创建的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$newId]);
            $newVersion = $stmt->fetch();
            
            sendSuccess($newVersion, '版本创建成功');
        } else {
            sendError('版本创建失败', 500);
        }
        
    } catch (Exception $e) {
        sendError('创建版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 删除版本
 */
function handleDeleteVersion($pdo) {
    $id = intval($_POST['id'] ?? 0);
    if ($id <= 0) {
        sendError('无效的版本ID');
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM app_updates WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success && $stmt->rowCount() > 0) {
            sendSuccess(null, '版本删除成功');
        } else {
            sendError('版本不存在或删除失败');
        }
        
    } catch (Exception $e) {
        sendError('删除版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 下载处理
 */
function handleDownload($pdo) {
    $id = intval($_GET['id'] ?? 0);
    $platform = strtolower($_GET['platform'] ?? 'windows');
    
    if ($id <= 0) {
        sendError('无效的版本ID');
    }
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ? AND status = 'published'");
        $stmt->execute([$id]);
        $version = $stmt->fetch();
        
        if (!$version) {
            sendError('版本不存在或未发布');
        }
        
        $downloadUrl = null;
        if ($platform === 'windows' && $version['exe_download_url']) {
            $downloadUrl = $version['exe_download_url'];
        } elseif ($platform === 'macos' && $version['dmg_download_url']) {
            $downloadUrl = $version['dmg_download_url'];
        }
        
        if (!$downloadUrl) {
            sendError('该平台的下载链接不存在');
        }
        
        // 更新下载次数
        $stmt = $pdo->prepare("UPDATE app_updates SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$id]);
        
        sendSuccess([
            'download_url' => $downloadUrl,
            'version' => $version['version'],
            'title' => $version['title']
        ], '获取下载链接成功');
        
    } catch (Exception $e) {
        sendError('获取下载链接失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新版本信息
 */
function handleUpdateVersion($pdo) {
    $id = intval($_POST['id'] ?? 0);
    if ($id <= 0) {
        sendError('无效的版本ID');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();
        
        if (!$version) {
            sendError('版本不存在');
        }
        
        // 更新字段
        $updateFields = [];
        $updateValues = [];

        if (isset($_POST['version'])) {
            $updateFields[] = 'version = ?';
            $updateValues[] = trim($_POST['version']);
        }

        if (isset($_POST['title'])) {
            $updateFields[] = 'title = ?';
            $updateValues[] = trim($_POST['title']);
        }

        if (isset($_POST['description'])) {
            $updateFields[] = 'description = ?';
            $updateValues[] = trim($_POST['description']);
        }

        if (isset($_POST['exe_download_url'])) {
            $updateFields[] = 'exe_download_url = ?';
            $updateValues[] = trim($_POST['exe_download_url']) ?: null;
        }

        if (isset($_POST['exe_backup_url'])) {
            $updateFields[] = 'exe_backup_url = ?';
            $updateValues[] = trim($_POST['exe_backup_url']) ?: null;
        }

        if (isset($_POST['dmg_download_url'])) {
            $updateFields[] = 'dmg_download_url = ?';
            $updateValues[] = trim($_POST['dmg_download_url']) ?: null;
        }

        if (isset($_POST['dmg_backup_url'])) {
            $updateFields[] = 'dmg_backup_url = ?';
            $updateValues[] = trim($_POST['dmg_backup_url']) ?: null;
        }

        if (isset($_POST['dmg_m1_download_url'])) {
            $updateFields[] = 'dmg_m1_download_url = ?';
            $updateValues[] = trim($_POST['dmg_m1_download_url']) ?: null;
        }

        if (isset($_POST['dmg_m1_backup_url'])) {
            $updateFields[] = 'dmg_m1_backup_url = ?';
            $updateValues[] = trim($_POST['dmg_m1_backup_url']) ?: null;
        }

        if (isset($_POST['dmg_intel_download_url'])) {
            $updateFields[] = 'dmg_intel_download_url = ?';
            $updateValues[] = trim($_POST['dmg_intel_download_url']) ?: null;
        }

        if (isset($_POST['dmg_intel_backup_url'])) {
            $updateFields[] = 'dmg_intel_backup_url = ?';
            $updateValues[] = trim($_POST['dmg_intel_backup_url']) ?: null;
        }

        if (isset($_POST['macos_architecture_support'])) {
            $updateFields[] = 'macos_architecture_support = ?';
            $updateValues[] = $_POST['macos_architecture_support'];
        }

        if (isset($_POST['force_update'])) {
            $updateFields[] = 'force_update = ?';
            $updateValues[] = intval($_POST['force_update']);
        }

        if (isset($_POST['status'])) {
            $updateFields[] = 'status = ?';
            $updateValues[] = $_POST['status'];
        }

        // 添加更新时间
        $updateFields[] = 'updated_at = NOW()';

        if (empty($updateFields) || count($updateFields) <= 1) { // 减去updated_at字段
            sendError('没有要更新的字段');
        }
        
        $updateValues[] = $id;
        
        $sql = "UPDATE app_updates SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $success = $stmt->execute($updateValues);
        
        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();
            
            sendSuccess($updatedVersion, '版本更新成功');
        } else {
            sendError('版本更新失败', 500);
        }
        
    } catch (Exception $e) {
        sendError('更新版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 发布版本
 */
function handlePublishVersion($pdo) {
    $id = $_POST['id'] ?? '';

    if (empty($id)) {
        sendError('版本ID不能为空');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在');
        }

        // 更新状态为已发布
        $stmt = $pdo->prepare("UPDATE app_updates SET status = 'published', updated_at = NOW() WHERE id = ?");
        $success = $stmt->execute([$id]);

        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();

            sendSuccess($updatedVersion, '版本发布成功');
        } else {
            sendError('版本发布失败', 500);
        }

    } catch (Exception $e) {
        sendError('发布版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 撤回版本
 */
function handleUnpublishVersion($pdo) {
    $id = $_POST['id'] ?? '';

    if (empty($id)) {
        sendError('版本ID不能为空');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在');
        }

        // 更新状态为草稿
        $stmt = $pdo->prepare("UPDATE app_updates SET status = 'draft', updated_at = NOW() WHERE id = ?");
        $success = $stmt->execute([$id]);

        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();

            sendSuccess($updatedVersion, '版本撤回成功');
        } else {
            sendError('版本撤回失败', 500);
        }

    } catch (Exception $e) {
        sendError('撤回版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 删除所有版本
 */
function handleDeleteAllVersions($pdo) {
    try {
        // 获取所有版本数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_updates");
        $count = $stmt->fetch()['count'];

        if ($count == 0) {
            sendSuccess(['deleted_count' => 0, 'deleted_files' => 0], '没有版本需要删除');
            return;
        }

        // 删除所有版本
        $stmt = $pdo->prepare("DELETE FROM app_updates");
        $success = $stmt->execute();

        if ($success) {
            sendSuccess([
                'deleted_count' => $count,
                'deleted_files' => 0 // 这里可以扩展文件删除功能
            ], "成功删除 {$count} 个版本");
        } else {
            sendError('删除失败', 500);
        }

    } catch (Exception $e) {
        sendError('删除所有版本失败: ' . $e->getMessage(), 500);
    }
}
?>
