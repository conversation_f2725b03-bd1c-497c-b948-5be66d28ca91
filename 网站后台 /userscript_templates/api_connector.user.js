// ==UserScript==
// @name         小梅花卡密连接器
// @namespace    https://xiaomeihuakefu.cn
// @version      2.0.0
// @description  自动连接小梅花API接口，验证卡密并执行脚本
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/kf*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_notification
// @grant        GM_registerMenuCommand
// @connect      xiaomeihuakefu.cn
// @connect      api.xiaomeihuakefu.cn
// @connect      secure.xiaomeihuakefu.cn
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置
    const CONFIG = {
        // API配置
        API_PRIMARY_SERVER: 'https://xiaomeihuakefu.cn',
        API_BACKUP_SERVER: 'https://api.xiaomeihuakefu.cn',
        API_SECURE_SERVER: 'https://secure.xiaomeihuakefu.cn',
        API_VERSION: 'v2',
        
        // 卡密配置
        LICENSE_KEY: '',
        
        // 安全配置
        ENCRYPTION_ENABLED: true,
        SIGNATURE_ENABLED: true,
        API_SECRET: '',
        
        // 心跳配置
        HEARTBEAT_INTERVAL: 5 * 60 * 1000, // 5分钟
        
        // 调试配置
        DEBUG_MODE: false
    };

    // 状态
    let STATE = {
        isVerified: false,
        securityToken: '',
        endpoint: '',
        functionType: '',
        hasCustomerService: false,
        hasProductListing: false,
        script: '',
        lastHeartbeat: 0,
        heartbeatTimer: null,
        retryCount: 0,
        maxRetries: 3
    };

    // 工具函数
    const Utils = {
        // 日志
        log: function(message, type = 'info') {
            if (CONFIG.DEBUG_MODE || type === 'error') {
                console.log(`小梅花卡密连接器] [${type.toUpperCase()}] ${message}`);
            }
        },
        
        // 通知
        notify: function(title, message, type = 'info') {
            GM_notification({
                title: title,
                text: message,
                timeout: 5000
            });
        },
        
        // 生成时间戳
        generateTimestamp: function() {
            return Math.floor(Date.now() / 1000).toString();
        },
        
        // 生成签名
        generateSignature: function(data, timestamp) {
            if (!CONFIG.SIGNATURE_ENABLED || !CONFIG.API_SECRET) {
                return '';
            }
            
            try {
                const signatureBase = JSON.stringify(data) + timestamp + CONFIG.API_SECRET;
                return this.sha256(signatureBase);
            } catch (error) {
                this.log('生成签名失败: ' + error.message, 'error');
                return '';
            }
        },
        
        // SHA-256哈希
        sha256: async function(message) {
            const msgBuffer = new TextEncoder().encode(message);
            const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        },
        
        // 加密数据
        encryptData: function(data) {
            if (!CONFIG.ENCRYPTION_ENABLED) {
                return data;
            }
            
            try {
                // 简单的Base64编码（实际项目中应使用更安全的加密方式）
                return btoa(JSON.stringify(data));
            } catch (error) {
                this.log('加密数据失败: ' + error.message, 'error');
                return data;
            }
        },
        
        // 解密数据
        decryptData: function(encryptedData) {
            if (!CONFIG.ENCRYPTION_ENABLED) {
                return encryptedData;
            }
            
            try {
                // 简单的Base64解码（实际项目中应使用更安全的解密方式）
                return JSON.parse(atob(encryptedData));
            } catch (error) {
                this.log('解密数据失败: ' + error.message, 'error');
                return null;
            }
        },
        
        // 获取客户端信息
        getClientInfo: function() {
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height
            };
        },
        
        // 获取店铺信息
        getShopInfo: function() {
            return {
                name: document.title,
                url: window.location.href,
                domain: window.location.hostname
            };
        },
        
        // 保存状态
        saveState: function() {
            GM_setValue('xiaomeihua_state', JSON.stringify({
                isVerified: STATE.isVerified,
                securityToken: STATE.securityToken,
                endpoint: STATE.endpoint,
                functionType: STATE.functionType,
                hasCustomerService: STATE.hasCustomerService,
                hasProductListing: STATE.hasProductListing,
                lastHeartbeat: STATE.lastHeartbeat
            }));
        },
        
        // 加载状态
        loadState: function() {
            const savedState = GM_getValue('xiaomeihua_state');
            if (savedState) {
                try {
                    const parsedState = JSON.parse(savedState);
                    STATE.isVerified = parsedState.isVerified || false;
                    STATE.securityToken = parsedState.securityToken || '';
                    STATE.endpoint = parsedState.endpoint || '';
                    STATE.functionType = parsedState.functionType || '';
                    STATE.hasCustomerService = parsedState.hasCustomerService || false;
                    STATE.hasProductListing = parsedState.hasProductListing || false;
                    STATE.lastHeartbeat = parsedState.lastHeartbeat || 0;
                } catch (error) {
                    this.log('加载状态失败: ' + error.message, 'error');
                }
            }
        },
        
        // 清除状态
        clearState: function() {
            STATE.isVerified = false;
            STATE.securityToken = '';
            STATE.endpoint = '';
            STATE.functionType = '';
            STATE.hasCustomerService = false;
            STATE.hasProductListing = false;
            STATE.script = '';
            STATE.lastHeartbeat = 0;
            
            if (STATE.heartbeatTimer) {
                clearInterval(STATE.heartbeatTimer);
                STATE.heartbeatTimer = null;
            }
            
            GM_deleteValue('xiaomeihua_state');
        }
    };

    // API接口
    const API = {
        // 构建API URL
        buildUrl: function(endpoint) {
            return `${CONFIG.API_PRIMARY_SERVER}/${CONFIG.API_VERSION}/api/${endpoint}`;
        },
        
        // 构建备用API URL
        buildBackupUrl: function(endpoint) {
            return `${CONFIG.API_BACKUP_SERVER}/${CONFIG.API_VERSION}/api/${endpoint}`;
        },
        
        // 发送请求
        sendRequest: function(endpoint, data, callback, useBackup = false) {
            const url = useBackup ? this.buildBackupUrl(endpoint) : this.buildUrl(endpoint);
            const timestamp = Utils.generateTimestamp();
            
            // 添加签名
            if (CONFIG.SIGNATURE_ENABLED) {
                data.signature = Utils.generateSignature(data, timestamp);
            }
            
            // 添加时间戳
            data.timestamp = timestamp;
            
            // 是否加密
            let requestData = data;
            if (CONFIG.ENCRYPTION_ENABLED) {
                requestData = {
                    encrypted: true,
                    data: Utils.encryptData(data)
                };
            }
            
            Utils.log(`发送请求到 ${url}`, 'debug');
            Utils.log(`请求数据: ${JSON.stringify(requestData)}`, 'debug');
            
            GM_xmlhttpRequest({
                method: 'POST',
                url: url,
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(requestData),
                timeout: 10000,
                onload: function(response) {
                    Utils.log(`收到响应: ${response.status}`, 'debug');
                    
                    try {
                        const responseData = JSON.parse(response.responseText);
                        
                        // 处理加密响应
                        if (responseData.encrypted && responseData.data) {
                            const decryptedData = Utils.decryptData(responseData.data);
                            if (decryptedData) {
                                callback(null, decryptedData);
                            } else {
                                callback(new Error('解密响应数据失败'), null);
                            }
                        } else {
                            callback(null, responseData);
                        }
                    } catch (error) {
                        Utils.log(`解析响应失败: ${error.message}`, 'error');
                        callback(error, null);
                    }
                },
                onerror: function(error) {
                    Utils.log(`请求错误: ${error.message || '未知错误'}`, 'error');
                    
                    // 如果主服务器失败，尝试备用服务器
                    if (!useBackup) {
                        Utils.log('尝试使用备用服务器', 'info');
                        API.sendRequest(endpoint, data, callback, true);
                    } else {
                        callback(error, null);
                    }
                },
                ontimeout: function() {
                    Utils.log('请求超时', 'error');
                    
                    // 如果主服务器超时，尝试备用服务器
                    if (!useBackup) {
                        Utils.log('尝试使用备用服务器', 'info');
                        API.sendRequest(endpoint, data, callback, true);
                    } else {
                        callback(new Error('请求超时'), null);
                    }
                }
            });
        },
        
        // 验证卡密
        verifyLicense: function(callback) {
            if (!CONFIG.LICENSE_KEY) {
                return callback(new Error('未设置卡密'), null);
            }
            
            const data = {
                key: CONFIG.LICENSE_KEY,
                shop_info: Utils.getShopInfo(),
                client_info: Utils.getClientInfo()
            };
            
            this.sendRequest('verify', data, function(error, response) {
                if (error) {
                    return callback(error, null);
                }
                
                if (!response.success) {
                    return callback(new Error(response.message || '验证失败'), null);
                }
                
                // 保存验证结果
                STATE.isVerified = true;
                STATE.securityToken = response.data.security_token || '';
                STATE.endpoint = response.data.endpoint || '';
                STATE.functionType = response.data.function_type || '';
                STATE.hasCustomerService = response.data.has_customer_service || false;
                STATE.hasProductListing = response.data.has_product_listing || false;
                STATE.script = response.data.script || '';
                STATE.lastHeartbeat = Date.now();
                
                // 保存状态
                Utils.saveState();
                
                callback(null, response.data);
            });
        },
        
        // 检查卡密状态
        checkStatus: function(callback) {
            if (!CONFIG.LICENSE_KEY) {
                return callback(new Error('未设置卡密'), null);
            }
            
            const data = {
                key: CONFIG.LICENSE_KEY,
                check_status: 1,
                client_info: Utils.getClientInfo()
            };
            
            this.sendRequest('status', data, function(error, response) {
                if (error) {
                    return callback(error, null);
                }
                
                if (!response.success) {
                    // 如果卡密失效，清除状态
                    if (response.error_code === 'KEY_EXPIRED' || response.error_code === 'KEY_DISABLED' || response.error_code === 'KEY_DELETED') {
                        Utils.clearState();
                    }
                    
                    return callback(new Error(response.message || '状态检查失败'), null);
                }
                
                // 更新状态
                STATE.securityToken = response.data.security_token || STATE.securityToken;
                STATE.functionType = response.data.function_type || STATE.functionType;
                STATE.hasCustomerService = response.data.has_customer_service || STATE.hasCustomerService;
                STATE.hasProductListing = response.data.has_product_listing || STATE.hasProductListing;
                
                // 保存状态
                Utils.saveState();
                
                callback(null, response.data);
            });
        },
        
        // 发送心跳
        sendHeartbeat: function(callback) {
            if (!CONFIG.LICENSE_KEY || !STATE.isVerified) {
                return callback && callback(new Error('未验证卡密'), null);
            }
            
            const data = {
                key: CONFIG.LICENSE_KEY,
                client_info: Utils.getClientInfo()
            };
            
            this.sendRequest('heartbeat', data, function(error, response) {
                if (error) {
                    return callback && callback(error, null);
                }
                
                if (!response.success) {
                    return callback && callback(new Error(response.message || '心跳失败'), null);
                }
                
                // 更新状态
                STATE.securityToken = response.data.security_token || STATE.securityToken;
                STATE.lastHeartbeat = Date.now();
                
                // 保存状态
                Utils.saveState();
                
                callback && callback(null, response.data);
            });
        }
    };

    // 脚本执行器
    const ScriptExecutor = {
        // 执行脚本
        execute: function() {
            if (!STATE.script) {
                Utils.log('没有可执行的脚本', 'warn');
                return;
            }
            
            try {
                Utils.log('开始执行脚本', 'info');
                
                // 创建一个安全的执行环境
                const scriptFunction = new Function(STATE.script);
                scriptFunction();
                
                Utils.log('脚本执行成功', 'info');
            } catch (error) {
                Utils.log(`脚本执行失败: ${error.message}`, 'error');
                Utils.notify('脚本执行失败', error.message, 'error');
            }
        }
    };

    // 初始化
    function initialize() {
        // 加载状态
        Utils.loadState();
        
        // 加载配置
        const savedKey = GM_getValue('xiaomeihua_license_key');
        if (savedKey) {
            CONFIG.LICENSE_KEY = savedKey;
        }
        
        // 注册菜单命令
        GM_registerMenuCommand('设置卡密', setLicenseKey);
        GM_registerMenuCommand('验证卡密', verifyLicenseKey);
        GM_registerMenuCommand('清除卡密', clearLicenseKey);
        
        // 如果已经验证过，检查状态
        if (STATE.isVerified) {
            checkLicenseStatus();
        } else if (CONFIG.LICENSE_KEY) {
            // 如果有卡密但未验证，进行验证
            verifyLicenseKey();
        }
    }

    // 设置卡密
    function setLicenseKey() {
        const key = prompt('请输入卡密:', CONFIG.LICENSE_KEY);
        if (key !== null) {
            CONFIG.LICENSE_KEY = key.trim();
            GM_setValue('xiaomeihua_license_key', CONFIG.LICENSE_KEY);
            Utils.notify('卡密已设置', '请点击验证卡密以激活脚本', 'info');
        }
    }

    // 验证卡密
    function verifyLicenseKey() {
        if (!CONFIG.LICENSE_KEY) {
            Utils.notify('错误', '请先设置卡密', 'error');
            return;
        }
        
        Utils.log('开始验证卡密', 'info');
        Utils.notify('验证中', '正在验证卡密，请稍候...', 'info');
        
        API.verifyLicense(function(error, data) {
            if (error) {
                Utils.log(`验证失败: ${error.message}`, 'error');
                Utils.notify('验证失败', error.message, 'error');
                
                // 重试
                if (STATE.retryCount < STATE.maxRetries) {
                    STATE.retryCount++;
                    Utils.log(`重试验证 (${STATE.retryCount}/${STATE.maxRetries})`, 'info');
                    setTimeout(verifyLicenseKey, 2000);
                } else {
                    STATE.retryCount = 0;
                }
                
                return;
            }
            
            Utils.log('卡密验证成功', 'info');
            Utils.notify('验证成功', '卡密验证成功，脚本已激活', 'info');
            
            // 重置重试计数
            STATE.retryCount = 0;
            
            // 执行脚本
            ScriptExecutor.execute();
            
            // 启动心跳
            startHeartbeat();
        });
    }

    // 清除卡密
    function clearLicenseKey() {
        CONFIG.LICENSE_KEY = '';
        GM_deleteValue('xiaomeihua_license_key');
        Utils.clearState();
        Utils.notify('卡密已清除', '已清除卡密和所有状态', 'info');
    }

    // 检查卡密状态
    function checkLicenseStatus() {
        Utils.log('检查卡密状态', 'info');
        
        API.checkStatus(function(error, data) {
            if (error) {
                Utils.log(`状态检查失败: ${error.message}`, 'error');
                
                // 如果是卡密问题，清除状态
                if (error.message.includes('expired') || error.message.includes('disabled') || error.message.includes('deleted')) {
                    Utils.clearState();
                    Utils.notify('卡密已失效', error.message, 'error');
                }
                
                return;
            }
            
            Utils.log('卡密状态正常', 'info');
            
            // 启动心跳
            startHeartbeat();
        });
    }

    // 启动心跳
    function startHeartbeat() {
        // 如果已经有心跳定时器，先清除
        if (STATE.heartbeatTimer) {
            clearInterval(STATE.heartbeatTimer);
        }
        
        // 设置心跳定时器
        STATE.heartbeatTimer = setInterval(function() {
            // 发送心跳
            API.sendHeartbeat();
        }, CONFIG.HEARTBEAT_INTERVAL);
        
        Utils.log('心跳已启动', 'info');
    }

    // 初始化
    initialize();
})(); 