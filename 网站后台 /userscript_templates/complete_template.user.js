// ==UserScript==
// @name         小梅花AI客服系统 - 完整模板
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  这是一个完整的UserScript模板，包含所有常用的头部配置
// <AUTHOR>
// @match        https://*/*
// @match        http://*/*
// @exclude      https://xiaomeihuakefu.cn/admin/*
// @connect      xiaomeihuakefu.cn
// @connect      api.xiaomeihuakefu.cn
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_addStyle
// @grant        GM_getResourceText
// @grant        GM_getResourceURL
// @grant        GM_openInTab
// @grant        GM_setClipboard
// @grant        GM_notification
// @grant        GM_registerMenuCommand
// @grant        GM_unregisterMenuCommand
// @grant        GM_log
// @grant        unsafeWindow
// @require      https://code.jquery.com/jquery-3.6.0.min.js
// @resource     customCSS https://xiaomeihuakefu.cn/assets/style.css
// @downloadURL  https://xiaomeihuakefu.cn/scripts/template.user.js
// @updateURL    https://xiaomeihuakefu.cn/scripts/template.user.js
// @supportURL   https://xiaomeihuakefu.cn/support
// @homepage     https://xiaomeihuakefu.cn
// @icon         https://xiaomeihuakefu.cn/favicon.ico
// @icon64       https://xiaomeihuakefu.cn/icon64.png
// @run-at       document-start
// @noframes
// ==/UserScript==

/*
 * 小梅花AI客服系统 - UserScript完整模板
 * 
 * 这个模板展示了如何创建一个功能完整的UserScript脚本
 * 包含了所有常用的Tampermonkey功能和最佳实践
 * 
 * 使用方法：
 * 1. 复制这个模板
 * 2. 修改头部信息（@name, @description等）
 * 3. 在主函数中添加你的功能代码
 * 4. 根据需要添加或删除@grant权限
 */

(function() {
    'use strict';
    
    // ==================== 配置区域 ====================
    const CONFIG = {
        // 脚本信息
        SCRIPT_NAME: '小梅花AI客服系统',
        SCRIPT_VERSION: '1.0.0',
        
        // API配置
        API_BASE: 'https://xiaomeihuakefu.cn/api/',
        API_TIMEOUT: 10000,
        
        // UI配置
        UI_POSITION: 'top-right', // top-left, top-right, bottom-left, bottom-right
        UI_THEME: 'auto', // light, dark, auto
        
        // 功能开关
        FEATURES: {
            autoSave: true,
            notifications: true,
            menuCommands: true,
            keyboardShortcuts: true
        },
        
        // 调试模式
        DEBUG: false
    };
    
    // ==================== 工具函数 ====================
    
    // 调试日志
    function debugLog(message, data = null) {
        if (CONFIG.DEBUG) {
            const timestamp = new Date().toISOString();
            console.log(`[${CONFIG.SCRIPT_NAME}] ${timestamp}: ${message}`, data || '');
            GM_log(`${message} ${data ? JSON.stringify(data) : ''}`);
        }
    }
    
    // 显示通知
    function showNotification(title, message, timeout = 3000) {
        if (!CONFIG.FEATURES.notifications) return;
        
        if (typeof GM_notification !== 'undefined') {
            GM_notification({
                title: title,
                text: message,
                timeout: timeout,
                onclick: function() {
                    window.focus();
                }
            });
        } else {
            // 降级到浏览器通知
            if (Notification.permission === 'granted') {
                new Notification(title, {
                    body: message,
                    icon: 'https://xiaomeihuakefu.cn/favicon.ico'
                });
            }
        }
    }
    
    // HTTP请求封装
    function makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const requestOptions = {
                method: options.method || 'GET',
                url: url,
                timeout: CONFIG.API_TIMEOUT,
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data);
                    } catch (e) {
                        resolve(response.responseText);
                    }
                },
                onerror: function(error) {
                    debugLog('请求失败', { url, error });
                    reject(error);
                },
                ontimeout: function() {
                    debugLog('请求超时', { url });
                    reject(new Error('请求超时'));
                }
            };
            
            // 添加请求头
            if (options.headers) {
                requestOptions.headers = options.headers;
            }
            
            // 添加请求数据
            if (options.data) {
                requestOptions.data = options.data;
            }
            
            GM_xmlhttpRequest(requestOptions);
        });
    }
    
    // 存储管理
    const Storage = {
        set: function(key, value) {
            try {
                GM_setValue(key, JSON.stringify(value));
                debugLog('存储数据', { key, value });
            } catch (e) {
                debugLog('存储失败', { key, error: e.message });
            }
        },
        
        get: function(key, defaultValue = null) {
            try {
                const value = GM_getValue(key);
                return value ? JSON.parse(value) : defaultValue;
            } catch (e) {
                debugLog('读取存储失败', { key, error: e.message });
                return defaultValue;
            }
        },
        
        remove: function(key) {
            try {
                GM_deleteValue(key);
                debugLog('删除存储', { key });
            } catch (e) {
                debugLog('删除存储失败', { key, error: e.message });
            }
        }
    };
    
    // ==================== UI组件 ====================
    
    // 创建主界面
    function createMainUI() {
        // 添加自定义样式
        GM_addStyle(`
            .xmh-main-panel {
                position: fixed;
                z-index: 999999;
                width: 320px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            .xmh-main-panel.top-right {
                top: 20px;
                right: 20px;
            }
            
            .xmh-main-panel.top-left {
                top: 20px;
                left: 20px;
            }
            
            .xmh-main-panel.bottom-right {
                bottom: 20px;
                right: 20px;
            }
            
            .xmh-main-panel.bottom-left {
                bottom: 20px;
                left: 20px;
            }
            
            .xmh-panel-header {
                padding: 20px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .xmh-panel-title {
                font-size: 16px;
                font-weight: 600;
                margin: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            
            .xmh-panel-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #999;
                transition: color 0.3s ease;
            }
            
            .xmh-panel-close:hover {
                color: #333;
            }
            
            .xmh-panel-body {
                padding: 20px;
            }
            
            .xmh-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.3s ease;
                margin: 4px;
            }
            
            .xmh-button:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
            
            .xmh-input {
                width: 100%;
                padding: 10px 12px;
                border: 1px solid rgba(0, 0, 0, 0.2);
                border-radius: 6px;
                font-size: 13px;
                transition: all 0.3s ease;
                margin-bottom: 10px;
                box-sizing: border-box;
            }
            
            .xmh-input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
            
            .xmh-hidden {
                display: none !important;
            }
            
            @media (prefers-color-scheme: dark) {
                .xmh-main-panel {
                    background: rgba(30, 30, 30, 0.95);
                    color: white;
                    border-color: rgba(255, 255, 255, 0.1);
                }
                
                .xmh-panel-header {
                    border-bottom-color: rgba(255, 255, 255, 0.1);
                }
                
                .xmh-panel-close {
                    color: #ccc;
                }
                
                .xmh-panel-close:hover {
                    color: white;
                }
                
                .xmh-input {
                    background: rgba(255, 255, 255, 0.1);
                    border-color: rgba(255, 255, 255, 0.2);
                    color: white;
                }
            }
        `);
        
        // 创建面板
        const panel = document.createElement('div');
        panel.className = `xmh-main-panel ${CONFIG.UI_POSITION}`;
        panel.innerHTML = `
            <div class="xmh-panel-header">
                <h3 class="xmh-panel-title">🌸 ${CONFIG.SCRIPT_NAME}</h3>
                <button class="xmh-panel-close" onclick="this.closest('.xmh-main-panel').classList.add('xmh-hidden')">×</button>
            </div>
            <div class="xmh-panel-body">
                <div style="margin-bottom: 15px;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                        版本: ${CONFIG.SCRIPT_VERSION} | 状态: 运行中
                    </div>
                </div>
                
                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                    <button class="xmh-button" onclick="window.xmhTestFunction()">测试功能</button>
                    <button class="xmh-button" onclick="window.xmhShowSettings()">设置</button>
                    <button class="xmh-button" onclick="window.xmhShowAbout()">关于</button>
                </div>
                
                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(0,0,0,0.1);">
                    <input type="text" class="xmh-input" placeholder="输入测试内容..." id="xmh-test-input">
                    <button class="xmh-button" onclick="window.xmhProcessInput()" style="width: 100%;">处理输入</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        debugLog('主界面已创建');
        
        return panel;
    }
    
    // ==================== 核心功能 ====================
    
    // 测试功能
    window.xmhTestFunction = function() {
        debugLog('测试功能被调用');
        showNotification(CONFIG.SCRIPT_NAME, '测试功能正常工作！');
        
        // 这里添加你的测试代码
        console.log('测试功能执行中...');
    };
    
    // 显示设置
    window.xmhShowSettings = function() {
        debugLog('显示设置面板');
        
        const settings = Storage.get('settings', {
            notifications: true,
            autoSave: true,
            theme: 'auto'
        });
        
        alert(`当前设置：\n通知: ${settings.notifications ? '开启' : '关闭'}\n自动保存: ${settings.autoSave ? '开启' : '关闭'}\n主题: ${settings.theme}`);
    };
    
    // 显示关于信息
    window.xmhShowAbout = function() {
        debugLog('显示关于信息');
        
        const aboutInfo = `
${CONFIG.SCRIPT_NAME} v${CONFIG.SCRIPT_VERSION}

这是一个基于Tampermonkey的用户脚本模板，
展示了如何创建功能完整的用户脚本。

功能特性：
• 现代化UI设计
• 数据持久化存储
• HTTP请求封装
• 通知系统
• 调试日志
• 键盘快捷键
• 菜单命令

开发团队：小梅花开发团队
网站：https://xiaomeihuakefu.cn
        `;
        
        alert(aboutInfo);
    };
    
    // 处理输入
    window.xmhProcessInput = function() {
        const input = document.getElementById('xmh-test-input');
        if (!input) return;
        
        const value = input.value.trim();
        if (!value) {
            showNotification('提示', '请输入内容');
            return;
        }
        
        debugLog('处理用户输入', { value });
        
        // 保存到存储
        if (CONFIG.FEATURES.autoSave) {
            const history = Storage.get('inputHistory', []);
            history.unshift({
                value: value,
                timestamp: Date.now()
            });
            
            // 只保留最近10条
            if (history.length > 10) {
                history.splice(10);
            }
            
            Storage.set('inputHistory', history);
        }
        
        // 这里添加你的处理逻辑
        showNotification('处理完成', `已处理: ${value}`);
        input.value = '';
    };
    
    // ==================== 菜单命令 ====================
    
    function setupMenuCommands() {
        if (!CONFIG.FEATURES.menuCommands) return;
        
        // 注册菜单命令
        GM_registerMenuCommand('显示主面板', function() {
            const panel = document.querySelector('.xmh-main-panel');
            if (panel) {
                panel.classList.remove('xmh-hidden');
            }
        });
        
        GM_registerMenuCommand('切换调试模式', function() {
            CONFIG.DEBUG = !CONFIG.DEBUG;
            Storage.set('debugMode', CONFIG.DEBUG);
            showNotification('调试模式', CONFIG.DEBUG ? '已开启' : '已关闭');
        });
        
        GM_registerMenuCommand('清除数据', function() {
            if (confirm('确定要清除所有保存的数据吗？')) {
                Storage.remove('settings');
                Storage.remove('inputHistory');
                Storage.remove('debugMode');
                showNotification('数据清除', '所有数据已清除');
            }
        });
        
        debugLog('菜单命令已设置');
    }
    
    // ==================== 键盘快捷键 ====================
    
    function setupKeyboardShortcuts() {
        if (!CONFIG.FEATURES.keyboardShortcuts) return;
        
        document.addEventListener('keydown', function(e) {
            // Ctrl + Shift + M: 显示/隐藏主面板
            if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                e.preventDefault();
                const panel = document.querySelector('.xmh-main-panel');
                if (panel) {
                    panel.classList.toggle('xmh-hidden');
                }
                debugLog('快捷键触发: 切换主面板');
            }
            
            // Ctrl + Shift + T: 测试功能
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                window.xmhTestFunction();
                debugLog('快捷键触发: 测试功能');
            }
        });
        
        debugLog('键盘快捷键已设置');
    }
    
    // ==================== 初始化 ====================
    
    function init() {
        debugLog('开始初始化脚本');
        
        // 从存储中恢复调试模式设置
        const savedDebugMode = Storage.get('debugMode');
        if (savedDebugMode !== null) {
            CONFIG.DEBUG = savedDebugMode;
        }
        
        debugLog('配置信息', CONFIG);
        
        // 等待页面加载完成
        const initUI = () => {
            createMainUI();
            setupMenuCommands();
            setupKeyboardShortcuts();
            
            // 显示启动通知
            showNotification(CONFIG.SCRIPT_NAME, `v${CONFIG.SCRIPT_VERSION} 已启动`);
            
            debugLog('脚本初始化完成');
        };
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initUI);
        } else {
            // 延迟一点时间确保页面完全加载
            setTimeout(initUI, 500);
        }
    }
    
    // 启动脚本
    init();
    
    // ==================== 全局导出 ====================
    
    // 将一些函数导出到全局，方便调试和外部调用
    unsafeWindow.XMH_SCRIPT = {
        version: CONFIG.SCRIPT_VERSION,
        config: CONFIG,
        storage: Storage,
        makeRequest: makeRequest,
        showNotification: showNotification,
        debugLog: debugLog
    };
    
    debugLog('脚本模板加载完成');
    
})(); 