<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI客服系统 - 智能客服解决方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
            position: relative;
        }
        
        /* 动态粒子背景 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 8s infinite linear;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }
        
        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 10;
        }
        
        /* 头部导航 */
        .header {
            padding: 20px 0;
            position: relative;
            z-index: 100;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }
        
        .nav-links a:hover {
            opacity: 0.8;
        }
        
        /* 主要内容区 */
        .hero {
            text-align: center;
            padding: 80px 0;
            position: relative;
        }
        
        .hero-content {
            position: relative;
            z-index: 10;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInDown 1s ease-out;
        }
        
        .hero .subtitle {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.9;
            animation: slideInUp 1s ease-out 0.2s both;
        }
        
        .hero .description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeIn 1s ease-out 0.4s both;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 功能特性 */
        .features {
            padding: 80px 0;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.15);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: fadeInUp 0.8s ease-out;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .feature-card p {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* AI能力展示 */
        .ai-capabilities {
            padding: 80px 0;
        }
        
        .ai-capabilities h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .capabilities-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .capability-item {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            border-left: 4px solid #fff;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .capability-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(10px);
        }
        
        .capability-item h4 {
            font-size: 1.3rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .capability-item p {
            font-size: 0.95rem;
            line-height: 1.5;
            opacity: 0.9;
        }
        
        /* CTA区域 */
        .cta {
            padding: 80px 0;
            text-align: center;
            background: rgba(0,0,0,0.1);
        }
        
        .cta h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .cta p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 40px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .cta-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        /* 底部 */
        .footer {
            padding: 40px 0;
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.2);
            background: rgba(0,0,0,0.1);
        }
        
        .footer p {
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .hero .description {
                font-size: 1rem;
            }
            
            .features h2,
            .ai-capabilities h2,
            .cta h2 {
                font-size: 2rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .capabilities-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 动态粒子背景 - 已禁用以提升性能 -->
    <!-- <div class="particles" id="particles"></div> -->
    
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">小梅花AI客服</div>
                <ul class="nav-links">
                    <li><a href="#features">功能特性</a></li>
                    <li><a href="#capabilities">AI能力</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>小梅花AI客服系统</h1>
                    <p class="subtitle">智能、高效、全天候的AI客服解决方案</p>
                    <p class="description">
                        基于先进的人工智能技术，为您的企业提供24小时不间断的智能客服服务。
                        通过深度学习和自然语言处理，让AI客服更懂您的客户需求，提升服务质量和效率。
                    </p>
                </div>
            </div>
        </section>

        <section id="features" class="features">
            <div class="container">
                <h2>核心功能特性</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🤖</span>
                        <h3>智能对话</h3>
                        <p>基于GPT技术的智能对话系统，能够理解复杂语义，提供准确、自然的回复，让客户感受到真人般的服务体验。</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🕐</span>
                        <h3>24小时在线</h3>
                        <p>全天候不间断服务，无论何时客户咨询，都能得到及时响应。节假日、深夜时段也能保持高质量的客服服务。</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">📦</span>
                        <h3>订单查询</h3>
                        <p>集成订单管理系统，客户可以通过自然语言查询订单状态、物流信息、退换货进度等，操作简单便捷。</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🧠</span>
                        <h3>自主学习</h3>
                        <p>AI具备强大的自主学习能力，通过分析历史对话数据，不断优化回复质量，提升问题解决率。</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">📚</span>
                        <h3>知识训练</h3>
                        <p>支持上传产品手册、FAQ文档，AI会自动学习产品知识，确保回答的专业性和准确性。</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🎯</span>
                        <h3>精准识别</h3>
                        <p>智能识别客户意图，自动分类问题类型，匹配最合适的解决方案，提高问题解决效率。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="capabilities" class="ai-capabilities">
            <div class="container">
                <h2>AI核心能力</h2>
                <div class="capabilities-list">
                    <div class="capability-item">
                        <h4>🔍 智能意图识别</h4>
                        <p>准确识别客户咨询意图，包括售前咨询、售后服务、投诉建议等，自动路由到对应处理流程。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>💬 多轮对话管理</h4>
                        <p>维护完整的对话上下文，支持复杂的多轮对话，确保对话的连贯性和逻辑性。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>📊 情感分析</h4>
                        <p>实时分析客户情绪状态，识别不满或紧急情况，及时调整回复策略或转接人工客服。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>🔄 实时学习</h4>
                        <p>从每次对话中学习，持续优化回复策略，知识库自动更新，服务质量不断提升。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>🌐 多平台集成</h4>
                        <p>支持微信、QQ、网站、APP等多个平台接入，统一管理，数据互通。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>📈 数据分析</h4>
                        <p>提供详细的服务数据分析，包括问题分布、解决率、满意度等关键指标。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>🛡️ 安全防护</h4>
                        <p>内置敏感词过滤、反垃圾机制，保护企业品牌形象，确保服务质量。</p>
                    </div>
                    
                    <div class="capability-item">
                        <h4>⚡ 快速响应</h4>
                        <p>毫秒级响应速度，支持高并发访问，即使在业务高峰期也能保持稳定服务。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="contact" class="cta">
            <div class="container">
                <h2>开启智能客服新时代</h2>
                <p>让AI为您的客户服务赋能，提升效率，降低成本，创造更好的客户体验</p>
                <a href="mailto:<EMAIL>" class="cta-button">联系我们</a>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 小梅花AI客服系统. 保留所有权利. | 技术支持：小梅花科技</p>
        </div>
    </footer>

    <script>
        // 粒子背景已禁用以提升性能
        console.log('粒子背景已禁用以提升性能');
    </script>
</body>
</html> 