<?php
header('Content-Type: text/plain; charset=utf-8');
require_once 'includes/db.php';

try {
    echo "=== 检查app_updates表结构 ===" . PHP_EOL;
    $stmt = $pdo->query('DESCRIBE app_updates');
    $columns = $stmt->fetchAll();
    foreach ($columns as $col) {
        echo $col['Field'] . ' - ' . $col['Type'] . ' - ' . $col['Null'] . ' - ' . $col['Default'] . PHP_EOL;
    }
    
    echo PHP_EOL . "=== 检查所有记录 ===" . PHP_EOL;
    $stmt = $pdo->query('SELECT * FROM app_updates ORDER BY created_at DESC');
    $records = $stmt->fetchAll();
    
    foreach ($records as $record) {
        echo "--- 记录 ID: " . $record['id'] . " ---" . PHP_EOL;
        foreach ($record as $key => $value) {
            echo $key . ': ' . ($value === null ? 'NULL' : "'" . $value . "'") . PHP_EOL;
        }
        echo PHP_EOL;
    }
    
    echo "总共 " . count($records) . " 条记录" . PHP_EOL;
    
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage() . PHP_EOL;
}
?>
