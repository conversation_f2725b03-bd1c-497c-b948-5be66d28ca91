<?php
/**
 * 修复app无限循环更新问题
 * 清理数据库中的无效版本记录
 */

header('Content-Type: text/plain; charset=utf-8');
require_once 'includes/db.php';

try {
    echo "=== 修复app无限循环更新问题 ===" . PHP_EOL;
    echo "开始时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;
    
    // 1. 检查当前问题记录
    echo "1. 检查当前问题记录..." . PHP_EOL;
    $stmt = $pdo->query("SELECT * FROM app_updates WHERE version IS NULL OR version = '' OR TRIM(version) = ''");
    $problemRecords = $stmt->fetchAll();
    
    echo "发现 " . count($problemRecords) . " 条问题记录:" . PHP_EOL;
    foreach ($problemRecords as $record) {
        echo "  - ID: {$record['id']}, 版本: '" . ($record['version'] ?? 'NULL') . "', 状态: {$record['status']}, 创建时间: {$record['created_at']}" . PHP_EOL;
    }
    echo PHP_EOL;
    
    // 2. 删除无效记录
    if (count($problemRecords) > 0) {
        echo "2. 删除无效记录..." . PHP_EOL;
        $stmt = $pdo->prepare("DELETE FROM app_updates WHERE version IS NULL OR version = '' OR TRIM(version) = ''");
        $deletedCount = $stmt->execute();
        echo "已删除 " . $stmt->rowCount() . " 条无效记录" . PHP_EOL . PHP_EOL;
    }
    
    // 3. 检查是否有有效的版本记录
    echo "3. 检查当前有效版本记录..." . PHP_EOL;
    $stmt = $pdo->query("SELECT * FROM app_updates WHERE version IS NOT NULL AND version != '' AND TRIM(version) != '' ORDER BY created_at DESC");
    $validRecords = $stmt->fetchAll();
    
    echo "当前有效版本记录 " . count($validRecords) . " 条:" . PHP_EOL;
    foreach ($validRecords as $record) {
        echo "  - ID: {$record['id']}, 版本: {$record['version']}, 状态: {$record['status']}, 平台: " . ($record['platform'] ?? 'all') . ", 创建时间: {$record['created_at']}" . PHP_EOL;
    }
    echo PHP_EOL;
    
    // 4. 如果没有有效记录，创建一个与当前app版本匹配的记录
    if (count($validRecords) == 0) {
        echo "4. 没有有效版本记录，创建默认记录..." . PHP_EOL;
        $currentAppVersion = '3.0.0'; // 从package.json中获取的当前版本
        
        $stmt = $pdo->prepare("
            INSERT INTO app_updates 
            (version, title, description, status, platform, force_update, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $currentAppVersion,
            "小梅花AI智能客服 v{$currentAppVersion}",
            "当前稳定版本",
            'published',
            'all',
            0
        ]);
        
        echo "已创建默认版本记录: {$currentAppVersion}" . PHP_EOL . PHP_EOL;
    }
    
    // 5. 最终验证
    echo "5. 最终验证..." . PHP_EOL;
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM app_updates");
    $totalCount = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as valid FROM app_updates WHERE version IS NOT NULL AND version != '' AND TRIM(version) != ''");
    $validCount = $stmt->fetch()['valid'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as published FROM app_updates WHERE status = 'published' AND version IS NOT NULL AND version != '' AND TRIM(version) != ''");
    $publishedCount = $stmt->fetch()['published'];
    
    echo "总记录数: {$totalCount}" . PHP_EOL;
    echo "有效记录数: {$validCount}" . PHP_EOL;
    echo "已发布记录数: {$publishedCount}" . PHP_EOL . PHP_EOL;
    
    if ($totalCount == $validCount && $publishedCount > 0) {
        echo "✅ 数据库清理完成！所有记录都是有效的。" . PHP_EOL;
    } else {
        echo "⚠️ 仍有问题记录，请手动检查。" . PHP_EOL;
    }
    
    echo PHP_EOL . "完成时间: " . date('Y-m-d H:i:s') . PHP_EOL;
    
} catch (Exception $e) {
    echo '❌ 错误: ' . $e->getMessage() . PHP_EOL;
    echo '堆栈跟踪: ' . $e->getTraceAsString() . PHP_EOL;
}
?>
