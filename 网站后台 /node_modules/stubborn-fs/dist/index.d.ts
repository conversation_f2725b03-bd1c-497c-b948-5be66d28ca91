/// <reference types="node" />
import fs from 'node:fs';
declare const FS: {
    attempt: {
        chmod: typeof fs.chmod.__promisify__;
        chown: typeof fs.chown.__promisify__;
        close: typeof fs.close.__promisify__;
        fsync: typeof fs.fsync.__promisify__;
        mkdir: typeof fs.mkdir.__promisify__;
        realpath: typeof fs.realpath.__promisify__;
        stat: typeof fs.stat.__promisify__;
        unlink: typeof fs.unlink.__promisify__;
        chmodSync: typeof fs.chmodSync;
        chownSync: typeof fs.chownSync;
        closeSync: typeof fs.closeSync;
        existsSync: typeof fs.existsSync;
        fsyncSync: typeof fs.fsync;
        mkdirSync: typeof fs.mkdirSync;
        realpathSync: typeof fs.realpathSync;
        statSync: fs.StatSyncFn;
        unlinkSync: typeof fs.unlinkSync;
    };
    retry: {
        close: (timeout: number) => typeof fs.close.__promisify__;
        fsync: (timeout: number) => typeof fs.fsync.__promisify__;
        open: (timeout: number) => typeof fs.open.__promisify__;
        readFile: (timeout: number) => typeof fs.readFile.__promisify__;
        rename: (timeout: number) => typeof fs.rename.__promisify__;
        stat: (timeout: number) => typeof fs.stat.__promisify__;
        write: (timeout: number) => typeof fs.write.__promisify__;
        writeFile: (timeout: number) => typeof fs.writeFile.__promisify__;
        closeSync: (timeout: number) => typeof fs.closeSync;
        fsyncSync: (timeout: number) => typeof fs.fsyncSync;
        openSync: (timeout: number) => typeof fs.openSync;
        readFileSync: (timeout: number) => typeof fs.readFileSync;
        renameSync: (timeout: number) => typeof fs.renameSync;
        statSync: (timeout: number) => fs.StatSyncFn;
        writeSync: (timeout: number) => typeof fs.writeSync;
        writeFileSync: (timeout: number) => typeof fs.writeFileSync;
    };
};
export default FS;
