{"name": "stubborn-fs", "repository": "github:fabios<PERSON><PERSON><PERSON><PERSON>/stubborn-fs", "description": "Stubborn versions of Node's fs functions that try really hard to do their job.", "version": "1.2.5", "type": "module", "main": "dist/index.js", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"clean": "tsex clean", "compile": "tsex compile", "compile:watch": "tsex compile --watch", "prepublishOnly": "tsex prepare"}, "keywords": ["fs", "attempt", "retry", "stubborn", "reliable"], "devDependencies": {"@types/node": "^18.13.0", "tsex": "^2.1.0", "typescript": "^4.9.5"}}