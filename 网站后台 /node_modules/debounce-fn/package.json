{"name": "debounce-fn", "version": "6.0.0", "description": "Debounce a function", "license": "MIT", "repository": "sindresorhus/debounce-fn", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["debounce", "function", "debouncer", "fn", "func", "throttle", "delay", "invoked"], "dependencies": {"mimic-function": "^5.0.0"}, "devDependencies": {"ava": "^5.3.1", "delay": "^6.0.0", "tsd": "^0.29.0", "xo": "^0.56.0"}}